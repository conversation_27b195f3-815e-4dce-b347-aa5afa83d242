2025-07-24 08:55:35.644 [main] INFO  org.scrm.ScrmGatewayApplication - The following 1 profile is active: "dev"
2025-07-24 08:55:35.673 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-24 08:55:35.673 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-24 08:55:35.674 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-24 08:55:35.674 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-24 08:55:35.674 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-24 08:55:35.674 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-24 08:55:35.674 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-24 08:55:37.188 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-24 08:55:37.193 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-24 08:55:37.271 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54 ms. Found 0 Redis repository interfaces.
2025-07-24 08:55:37.672 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[org.scrm]' package. Please check your configuration.
2025-07-24 08:55:37.825 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=24301223-7113-33b5-b484-079c86d537e1
2025-07-24 08:55:38.408 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.421 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.425 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.433 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.441 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.446 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.448 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$465/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.456 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.469 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.538 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.555 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.659 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-24 08:55:38.664 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.666 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:38.667 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:40.890 [main] INFO  o.s.g.s.GatewayDynamicRouteService - gateway route init...
2025-07-24 08:55:41.905 [main] INFO  o.s.g.s.GatewayDynamicRouteService - 获取网关当前配置:
[{
	"id": "scrm-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/auth/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"name": "ValidateCodeFilter"
	}, {
		"name": "CacheRequestFilter"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-auth-system",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/system/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-wechat-api",
	"order": 2,
	"predicates": [{
		"args": {
			"pattern": "/wecom/**"
		},
		"name": "Path"
	}],
	"filters": [{
		"args": {
			"_genkey_0": "1"
		},
		"name": "StripPrefix"
	}],
	"uri": "lb://scrm-wechat-api"
}, {
	"id": "scrm-web-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/open/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-web-api"
}, {
	"id": "scrm-file",
	"order": 4,
	"predicates": [{
		"args": {
			"pattern": "/file/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-file"
}, {
	"id": "scrm-auth-common",
	"order": 0,
	"predicates": [{
		"args": {
			"pattern": "/common/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-system"
}, {
	"id": "scrm-mobile-api",
	"order": 3,
	"predicates": [{
		"args": {
			"pattern": "/wx-api/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-mobile-api"
}, {
	"id": "scrm-open-ai",
	"order": 6,
	"predicates": [{
		"args": {
			"pattern": "/ai/**"
		},
		"name": "Path"
	}],
	"uri": "lb://scrm-open-ai"
}]
2025-07-24 08:55:41.933 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-system', predicates=[PredicateDefinition{name='Path', args={pattern=/auth/**}}], filters=[FilterDefinition{name='ValidateCodeFilter', args={}}, FilterDefinition{name='CacheRequestFilter', args={}}], uri=lb://scrm-system, order=0, metadata={}}
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-24 08:55:44.011 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-24 08:55:44.012 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-24 08:55:44.012 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-24 08:55:44.012 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-24 08:55:44.012 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-24 08:55:44.196 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-24 08:55:44.197 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-24 08:55:44.198 [boundedElastic-1] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-24 08:55:44.380 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-system', predicates=[PredicateDefinition{name='Path', args={pattern=/system/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-24 08:55:44.389 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-wechat-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wecom/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://scrm-wechat-api, order=2, metadata={}}
2025-07-24 08:55:44.399 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-web-api', predicates=[PredicateDefinition{name='Path', args={pattern=/open/**}}], filters=[], uri=lb://scrm-web-api, order=3, metadata={}}
2025-07-24 08:55:44.409 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-file', predicates=[PredicateDefinition{name='Path', args={pattern=/file/**}}], filters=[], uri=lb://scrm-file, order=4, metadata={}}
2025-07-24 08:55:44.416 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-auth-common', predicates=[PredicateDefinition{name='Path', args={pattern=/common/**}}], filters=[], uri=lb://scrm-system, order=0, metadata={}}
2025-07-24 08:55:44.426 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-mobile-api', predicates=[PredicateDefinition{name='Path', args={pattern=/wx-api/**}}], filters=[], uri=lb://scrm-mobile-api, order=3, metadata={}}
2025-07-24 08:55:44.433 [main] INFO  o.s.g.s.GatewayDynamicRouteService - update route : RouteDefinition{id='scrm-open-ai', predicates=[PredicateDefinition{name='Path', args={pattern=/ai/**}}], filters=[], uri=lb://scrm-open-ai, order=6, metadata={}}
2025-07-24 08:55:44.444 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] gateway-router+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:55:44.447 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=gateway-router, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:55:45.198 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -**********
2025-07-24 08:55:45.534 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-07-24 08:55:45.792 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-24 08:55:47.065 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-24 08:55:47.710 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-24 08:55:47.711 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-24 08:55:49.012 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-24 08:55:49.346 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 6180
2025-07-24 08:55:49.722 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6180, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-gateWay', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-24 08:55:49.722 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-gateWay with instance: Instance{instanceId='null', ip='**************', port=6180, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-24 08:55:49.770 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-gateWay **************:6180 register finished
2025-07-24 08:55:49.836 [main] INFO  org.scrm.ScrmGatewayApplication - Started ScrmGatewayApplication in 16.828 seconds (JVM running for 18.274)
2025-07-24 08:55:49.840 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:55:49.840 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:55:49.841 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:55:49.841 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:55:49.842 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:55:49.843 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:55:49.844 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-gateWay+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:55:49.844 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-gateWay, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:55:49.866 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-24 08:55:49.873 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-24 08:55:50.352 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-24 08:55:50.354 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-gateWay@@DEFAULT -> [{"instanceId":"**************#6180#DEFAULT#DEFAULT_GROUP@@scrm-gateWay","ip":"**************","port":6180,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-gateWay","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-24 08:56:19.407 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-24 08:56:19.410 [boundedElastic-4] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-24 08:56:49.421 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-24 08:56:49.423 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-24 08:57:05.995 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753318625995-3982
2025-07-24 08:57:07.683 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753318627683-8018
2025-07-24 08:57:07.683 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753318627683-5061
2025-07-24 08:57:08.337 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailTab/1948018399404908545 业务请求traceId:iyque-1753318628337-8958
2025-07-24 08:57:08.338 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopDetailCustomer 业务请求traceId:iyque-1753318628338-7047
2025-07-24 08:57:08.403 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948018399404908545 业务请求traceId:iyque-1753318628402-7410
2025-07-24 08:57:10.955 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753318630955-9313
2025-07-24 09:00:29.264 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753318829264-19
2025-07-24 09:00:29.450 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753318829450-9817
2025-07-24 09:00:29.450 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753318829450-7818
2025-07-24 09:00:29.896 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753318829896-8676
2025-07-24 09:00:33.678 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753318833678-8776
2025-07-24 09:00:42.287 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753318842287-6662
2025-07-24 09:00:42.446 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753318842446-5120
2025-07-24 09:00:42.446 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753318842446-8458
2025-07-24 09:00:42.803 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753318842803-7170
2025-07-24 09:01:30.440 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.http.ServerHttpAgent - [NACOS SocketTimeoutException httpPost] currentServerAddr: http://121.40.36.84:8848， err : Read timed out
2025-07-24 09:01:30.441 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.http.ServerHttpAgent - no available server, currentServerAddr : http://121.40.36.84:8848
2025-07-24 09:01:30.444 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [check-update] get changed dataId exception
java.net.ConnectException: no available server, currentServerAddr : http://121.40.36.84:8848
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpPost(ServerHttpAgent.java:190)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpPost(MetricsHttpAgent.java:67)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateConfigStr(ClientWorker.java:380)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateDataIds(ClientWorker.java:347)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:535)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-07-24 09:01:30.444 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.impl.ClientWorker - longPolling error : 
java.net.ConnectException: no available server, currentServerAddr : http://121.40.36.84:8848
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpPost(ServerHttpAgent.java:190)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpPost(MetricsHttpAgent.java:67)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateConfigStr(ClientWorker.java:380)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateDataIds(ClientWorker.java:347)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:535)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-07-24 09:02:30.259 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753318950259-2159
2025-07-24 09:02:30.449 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753318950449-2664
2025-07-24 09:02:30.449 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753318950449-8735
2025-07-24 09:02:30.801 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753318950801-5574
2025-07-24 09:02:32.682 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948018399404908545 业务请求traceId:iyque-1753318952682-3052
2025-07-24 09:02:32.684 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753318952684-2858
2025-07-24 09:02:32.689 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753318952689-5308
2025-07-24 09:02:33.133 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753318953133-9320
2025-07-24 09:02:37.194 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753318957194-8558
2025-07-24 09:02:39.499 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948018399404908545 业务请求traceId:iyque-1753318959499-8877
2025-07-24 09:02:39.499 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753318959499-7355
2025-07-24 09:02:39.499 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753318959499-7020
2025-07-24 09:02:39.856 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753318959856-3467
2025-07-24 09:02:43.999 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/customer/findAllWeCustomerList 业务请求traceId:iyque-1753318963999-4714
2025-07-24 09:18:44.386 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753319924386-7337
2025-07-24 09:18:44.748 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753319924748-2486
2025-07-24 09:18:44.748 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753319924748-788
2025-07-24 09:18:45.798 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753319925798-9221
2025-07-24 09:18:45.798 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753319925798-7440
2025-07-24 09:18:45.798 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948018399404908545 业务请求traceId:iyque-1753319925798-5213
2025-07-24 09:18:46.539 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753319926539-6984
2025-07-24 09:27:12.782 [reactor-http-nio-1] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753320432782-274
2025-07-24 09:27:12.971 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753320432971-4255
2025-07-24 09:27:12.983 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753320432983-8438
2025-07-24 09:27:14.980 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/getDetail/1948018399404908545 业务请求traceId:iyque-1753320434980-311
2025-07-24 09:27:14.981 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/form/survey/list 业务请求traceId:iyque-1753320434981-7008
2025-07-24 09:27:14.981 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/groupCode/list 业务请求traceId:iyque-1753320434981-1315
2025-07-24 09:27:15.845 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/strackStage/findWeStrackStage 业务请求traceId:iyque-1753320435845-1354
2025-07-24 09:38:05.012 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753321085012-337
2025-07-24 09:38:05.182 [reactor-http-nio-5] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753321085182-3827
2025-07-24 09:38:05.184 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753321085184-485
2025-07-24 09:38:05.668 [reactor-http-nio-8] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753321085668-3640
2025-07-24 09:38:07.394 [reactor-http-nio-2] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getInfo 业务请求traceId:iyque-1753321087394-3600
2025-07-24 09:38:07.539 [reactor-http-nio-3] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/user/getRouters 业务请求traceId:iyque-1753321087538-5983
2025-07-24 09:38:07.539 [reactor-http-nio-4] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/system/config/get/config 业务请求traceId:iyque-1753321087538-6386
2025-07-24 09:38:07.821 [reactor-http-nio-6] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753321087821-8343
2025-07-24 10:15:23.941 [reactor-http-nio-7] INFO  org.scrm.gateway.filter.AuthFilter - 业务请求的url:/open/sop/findWeSopLists 业务请求traceId:iyque-1753323323941-7267
