2025-07-24 09:41:17.276 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.http.ServerHttpAgent - [NACOS SocketTimeoutException httpPost] currentServerAddr: http://121.40.36.84:8848， err : Read timed out
2025-07-24 09:41:17.280 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.http.ServerHttpAgent - no available server, currentServerAddr : http://121.40.36.84:8848
2025-07-24 09:41:17.283 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [check-update] get changed dataId exception
java.net.ConnectException: no available server, currentServerAddr : http://121.40.36.84:8848
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpPost(ServerHttpAgent.java:190)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpPost(MetricsHttpAgent.java:67)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateConfigStr(ClientWorker.java:380)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateDataIds(ClientWorker.java:347)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:535)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-07-24 09:41:17.284 [com.alibaba.nacos.client.Worker.longPolling.fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.impl.ClientWorker - longPolling error : 
java.net.ConnectException: no available server, currentServerAddr : http://121.40.36.84:8848
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpPost(ServerHttpAgent.java:190)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpPost(MetricsHttpAgent.java:67)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateConfigStr(ClientWorker.java:380)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateDataIds(ClientWorker.java:347)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:535)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
