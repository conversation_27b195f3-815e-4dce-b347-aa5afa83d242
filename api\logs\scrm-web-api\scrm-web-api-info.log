2025-07-24 08:55:39.432 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-24 08:55:39.469 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-24 08:55:39.469 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-24 08:55:39.469 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-24 08:55:39.469 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-24 08:55:39.469 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-24 08:55:39.470 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-24 08:55:39.470 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-24 08:55:42.492 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-24 08:55:42.495 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-24 08:55:42.750 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 234 ms. Found 0 Redis repository interfaces.
2025-07-24 08:55:43.420 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-24 08:55:43.936 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$ae0b875f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:43.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:43.998 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.003 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.010 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.020 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.023 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.024 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.036 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.051 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.124 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.144 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:44.603 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-24 08:55:45.111 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-24 08:55:45.124 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-24 08:55:45.125 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 08:55:45.125 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-24 08:55:45.454 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-24 08:55:45.454 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5984 ms
2025-07-24 08:55:46.175 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:55:46.826 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-24 08:55:48.544 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-24 08:55:57.220 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:55:57.232 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:55:57.871 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:55:58.973 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:55:59.817 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-24 08:56:00.339 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-24 08:56:00.351 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-24 08:56:00.838 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:00.863 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:01.669 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:03.132 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:03.696 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:03.967 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:06.343 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:06.800 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:08.165 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:08.238 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:09.660 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:10.521 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:11.945 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-24 08:56:11.946 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-24 08:56:13.105 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-24 08:56:13.395 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-24 08:56:13.396 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-24 08:56:13.396 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-24 08:56:13.611 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-24 08:56:13.652 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-24 08:56:13.655 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-24 08:56:13.655 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-24 08:56:13.691 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-24 08:56:13.861 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 37.153 seconds (JVM running for 39.052)
2025-07-24 08:56:13.890 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:56:13.892 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:56:13.894 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:56:13.894 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:56:13.894 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:56:13.894 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:56:13.895 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:56:13.895 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:56:14.679 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-24 08:56:14.685 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-24 08:57:08.480 [http-nio-6091-exec-3] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 08:57:08.481 [http-nio-6091-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 08:57:08.486 [http-nio-6091-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-24 08:57:11.037 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-24 09:00:29.905 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-24 09:00:33.683 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-24 09:00:42.808 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-24 09:02:30.811 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-24 09:02:32.742 [http-nio-6091-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4027729b[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:02:32.762 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@18d2dc75[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:02:37.199 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-24 09:02:39.506 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@5b31ac51[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:02:39.506 [http-nio-6091-exec-9] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4fb4aca7[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:02:44.031 [http-nio-6091-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@1cd722ce[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:18:45.809 [http-nio-6091-exec-10] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3f0404e7[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:18:45.809 [http-nio-6091-exec-7] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@43272e61[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:27:14.996 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3cac012[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:27:14.997 [http-nio-6091-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@6e92d3e5[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:38:05.673 [http-nio-6091-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-24 09:38:07.826 [http-nio-6091-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
2025-07-24 09:41:17.276 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.http.ServerHttpAgent - [NACOS SocketTimeoutException httpPost] currentServerAddr: http://************:8848， err : Read timed out
2025-07-24 09:41:17.280 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.http.ServerHttpAgent - no available server, currentServerAddr : http://************:8848
2025-07-24 09:41:17.283 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.impl.ClientWorker - [fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [check-update] get changed dataId exception
java.net.ConnectException: no available server, currentServerAddr : http://************:8848
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpPost(ServerHttpAgent.java:190)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpPost(MetricsHttpAgent.java:67)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateConfigStr(ClientWorker.java:380)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateDataIds(ClientWorker.java:347)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:535)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-07-24 09:41:17.284 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] ERROR c.a.n.c.config.impl.ClientWorker - longPolling error : 
java.net.ConnectException: no available server, currentServerAddr : http://************:8848
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpPost(ServerHttpAgent.java:190)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpPost(MetricsHttpAgent.java:67)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateConfigStr(ClientWorker.java:380)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateDataIds(ClientWorker.java:347)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:535)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-07-24 10:15:23.946 [http-nio-6091-exec-2] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:null
