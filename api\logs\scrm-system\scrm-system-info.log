2025-07-24 08:55:43.831 [restartedMain] INFO  org.scrm.ScrmSystemApplication - The following 1 profile is active: "dev"
2025-07-24 08:55:43.870 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-24 08:55:43.870 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-24 08:55:43.871 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-24 08:55:43.871 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-24 08:55:43.871 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-24 08:55:43.871 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-24 08:55:43.871 [restartedMain] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-24 08:55:46.199 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-24 08:55:46.203 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-24 08:55:46.418 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 198 ms. Found 0 Redis repository interfaces.
2025-07-24 08:55:47.140 [restartedMain] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=bbaafe60-3136-36a4-aeea-fca110465031
2025-07-24 08:55:47.779 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$2af98a53] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.883 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.892 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.896 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.907 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.915 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.919 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.921 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$527/569092408] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.934 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:47.949 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:48.033 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:48.065 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-24 08:55:48.635 [restartedMain] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-24 08:55:49.811 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7880 (http)
2025-07-24 08:55:49.823 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7880"]
2025-07-24 08:55:49.823 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 08:55:49.823 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-24 08:55:49.900 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 08:55:49.900 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6029 ms
2025-07-24 08:55:52.607 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-24 08:55:53.983 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-24 08:56:08.728 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-24 08:56:08.846 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:09.899 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:09.923 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:10.659 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:11.047 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:11.316 [restartedMain] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-24 08:56:11.442 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-24 08:56:11.451 [redisson-netty-5-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-24 08:56:12.071 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:12.286 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:12.297 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:13.999 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:14.368 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:14.418 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:16.098 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:17.871 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:18.325 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:18.928 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:18.984 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:20.345 [restartedMain] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-24 08:56:22.715 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-24 08:56:22.716 [restartedMain] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-24 08:56:25.655 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-24 08:56:26.695 [restartedMain] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-24 08:56:27.008 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-24 08:56:27.009 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-24 08:56:27.010 [restartedMain] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-24 08:56:27.246 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7880"]
2025-07-24 08:56:27.328 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7880 (http) with context path ''
2025-07-24 08:56:27.332 [restartedMain] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=7880, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-system', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-24 08:56:27.333 [restartedMain] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-system with instance: Instance{instanceId='null', ip='**************', port=7880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-24 08:56:27.370 [restartedMain] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-system **************:7880 register finished
2025-07-24 08:56:27.551 [restartedMain] INFO  org.scrm.ScrmSystemApplication - Started ScrmSystemApplication in 46.437 seconds (JVM running for 48.408)
2025-07-24 08:56:27.559 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:56:27.561 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:56:27.562 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:56:27.562 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:56:27.562 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:56:27.562 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:56:27.563 [restartedMain] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-system.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-24 08:56:27.563 [restartedMain] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-system.yml, group=DEFAULT_GROUP, cnt=1
2025-07-24 08:56:28.344 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:56:28.352 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-system@@DEFAULT -> [{"instanceId":"**************#7880#DEFAULT#DEFAULT_GROUP@@scrm-system","ip":"**************","port":7880,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:57:07.025 [http-nio-7880-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 08:57:07.025 [http-nio-7880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-24 08:57:07.028 [http-nio-7880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-24 08:57:07.205 [http-nio-7880-exec-1] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@51335aa[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:00:29.286 [http-nio-7880-exec-6] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@71ac189c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:00:42.301 [http-nio-7880-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@574e22c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:02:30.281 [http-nio-7880-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@4c3ecf4d[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:18:44.414 [http-nio-7880-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@26f52db8[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:27:12.798 [http-nio-7880-exec-4] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@548f8d4c[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:38:05.032 [http-nio-7880-exec-8] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@3697048e[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
2025-07-24 09:38:07.405 [http-nio-7880-exec-3] INFO  org.scrm.base.aop.DataScopeAspect - 数据权限拦截:LoginUser(token=01f5ccc6-6f65-494a-a4b0-2464b188e902, corpId=ww07f7d26a5e8026e5, corpName=中妇, isLessor=null, userId=1, userName=超管, userType=00, loginTime=1753278586902, expireTime=1753321786902, ipaddr=127.0.0.1, permissions=null, roles=[], roleIds=null, mainSource=null, sysUser=org.scrm.base.core.domain.entity.SysUser@71f733f9[
  userId=1
  userName=超管
  nickName=admin
  email=
  bizMail=<null>
  phonenumber=
  sex=0
  avatar=https://iyque.oss-cn-shanghai.aliyuncs.com/2025/06/12/4d3c4db9-83bd-406a-8d81-17b64b742186.png
  password=$2a$10$A7C0ubeLeSBRZKmGbcUYL.RDBhYUz/PeQbjnPgG5KjP0qO4RsVCRi
  salt=<null>
  status=0
  delFlag=0
  loginIp=
  loginDate=<null>
  createBy=<null>
  createTime=Wed Sep 07 14:58:07 CST 2022
  updateBy=<null>
  updateTime=<null>
  remark=<null>
], filterType=null, businessIds=null, weUserIds=null, chatIds=null, extIds=null, lastFlag=null)
